@tailwind base;
@tailwind components;
@tailwind utilities;

.login-page-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.login-form-shadow {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.login-button-hover {
  transition: all 0.2s ease-in-out;
}

.login-button-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-app {
  height: 100dvh;
}

.markdown-body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
}

.markdown-body h1 {
  font-size: 1.2em !important;
}

.markdown-body h2 {
  font-size: 1.1em !important;
}

.markdown-body h3 {
  font-size: 1.05em !important;
}

.markdown-body hr {
  height: 1px !important;
}

@media (max-width: 767px) {
  .markdown-body {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.markdown-body menu,
ol {
  list-style: decimal;
}

.markdown-body ul {
  list-style: disc;
}

.markdown-body p {
  text-align: left;
}

.answer-content ol,
.answer-content ul {
  padding-left: 1.2em !important;
}

.hljs {
  background-color: transparent !important;
}

.new-chat-button {
  cursor: pointer;
  color: #0057ff;
  background-color: rgba(0, 87, 255, 0.06);
  border: .5px solid rgba(0, 102, 255, .15);
  font-weight: 500;
}

.new-chat-button:hover {
  background-color: rgba(0, 87, 255, 0.1);
}

.max-h-82 {
  max-height: 20.5rem;
}

.text-13 {
  font-size: 13px;
}

/* loading start */

.dots {
  width: 1.5em;
  height: 1.5em;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
}

.dots div {
  width: 0.4em;
  height: 0.4em;
  border-radius: 50%;
  background-color: #333;
  animation: fade 0.8s ease-in-out alternate infinite;
}

.dots div:nth-of-type(1) {
  animation-delay: -0.4s;
}

.dots div:nth-of-type(2) {
  animation-delay: -0.2s;
}

@keyframes fade {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.balls {
  width: 24px;
  height: 24px;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
}

.balls div {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #333;
  transform: translateY(-100%);
  animation: wave 0.8s ease-in-out alternate infinite;
}

.balls div:nth-of-type(1) {
  animation-delay: -0.4s;
}

.balls div:nth-of-type(2) {
  animation-delay: -0.2s;
}

@keyframes wave {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(100%);
  }
}

.spin-loading {
  border: 3px solid #ddd;
  border-top-color: #666;
  border-radius: 50%;
  width: 1.3em;
  height: 1.3em;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* loading end */

/* 添加 CSS 动画效果 */
.transition-transform {
  transition: transform 0.3s ease-in-out;
}

.transition-all {
  transition: all 0.3s ease-in-out;
}

.-translate-x-full {
  transform: translateX(-100%);
}

.model-select-dropdown ::-webkit-scrollbar {
  width: 6px;
}

.model-select-dropdown ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.model-select-dropdown ::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.model-select-dropdown ::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.markdown-body .footnotes {
  background-color: #f3f4f6;
  padding: 8px 16px;
  border-radius: 6px;
  border-top: none !important;
}

.footnotes h2 {
  margin-top: 0.8rem !important;
}

.data-footnote-backref {
  display: none; 
}

.markdown-body li>p {
  margin-top: 0.2rem !important;
  margin-bottom: 0.2rem !important;
}

.footnotes > h2:first-child,
.footnotes > h3:first-child {
  display: none;
}

.footnotes::before {
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #dedede;
  content: '引用内容';
  display: block;
  font-size: 1.1em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 1em;
}

.markdown-body [data-footnote-ref]::before {
  content: "" !important;
}

.markdown-body [data-footnote-ref]::after {
  content: ""  !important;
}